"use client";

import { useState } from "react";

import { But<PERSON> } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";

import {
  MatchJobCard,
  MatchNegotiationPanel,
  MatchProvider,
  MatchProviderCard,
  MatchRateInsights,
  MatchRateNegotiation,
  MatchWidget,
} from "./index";

// Mock match data for demonstration
const mockMatch = {
  id: "match-123",
  status: "NEGOTIATING" as const,
  initiator: "PROVIDER" as const,
  initiationNote:
    "I'm interested in this position and would like to discuss the rate.",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  jobId: "job-456",
  providerId: "provider-789",
  organizationId: "org-101",

  job: {
    id: "job-456",
    status: "PUBLISHED" as const,
    summary: "Emergency Department Physician",
    role: "Emergency Medicine",
    paymentType: "HOURLY" as const,
    paymentAmount: 150,
    allowRateNegotiation: true,
    minNegotiableRate: 120,
    maxNegotiableRate: 200,
  },

  provider: {
    id: "provider-789",
    title: "Emergency Medicine Physician",
    person: {
      id: "person-111",
      firstName: "Dr. Sarah",
      lastName: "Johnson",
      avatar: null,
    },
  },

  organization: {
    id: "org-101",
    name: "City General Hospital",
    avatar: null,
  },

  compensation: {
    id: "comp-222",
    minRate: 120,
    maxRate: 200,
    currentOfferRate: 165,
    finalAgreedRate: null,
    rateStrategy: "balanced",
    negotiationCount: 2,
    negotiationStatus: "ACTIVE",
    lastOfferBy: "org-101",
    offerExpiresAt: null,
    paymentType: "HOURLY" as const,
    updatedAt: new Date().toISOString(),
  },
};

export function MatchExample() {
  const [view, setView] = useState<"full" | "rate-only" | "components">("full");
  const [loading, setLoading] = useState(false);

  // Handlers are now managed by MatchContext

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Match Widget Examples</h1>
        <div className="flex gap-2">
          <Button
            variant={view === "full" ? "primary" : "outline"}
            onClick={() => setView("full")}
          >
            Full Widget
          </Button>
          <Button
            variant={view === "rate-only" ? "primary" : "outline"}
            onClick={() => setView("rate-only")}
          >
            Rate Only
          </Button>
          <Button
            variant={view === "components" ? "primary" : "outline"}
            onClick={() => setView("components")}
          >
            Individual Components
          </Button>
        </div>
      </div>

      <Card className="p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">
            {view === "full" && "Complete Match Cockpit"}
            {view === "rate-only" && "Rate Negotiation Panel"}
            {view === "components" && "Individual Components"}
          </h2>
          <p className="text-sm text-muted-foreground">
            {view === "full" &&
              "Full match interface with provider info, job details, rate insights, and negotiation timeline"}
            {view === "rate-only" &&
              "Focused rate negotiation interface using the negotiation timeline"}
            {view === "components" &&
              "Individual components with data/presentation separation using context"}
          </p>
        </div>

        {view === "full" && (
          <MatchWidget
            matchId={mockMatch.id}
            onViewJobDetails={() => alert("View job details")}
          />
        )}

        {view === "rate-only" && (
          <div className="max-w-2xl">
            <MatchRateNegotiation matchId={mockMatch.id} loading={loading} />
          </div>
        )}

        {view === "components" && (
          <MatchProvider matchId={mockMatch.id}>
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <div className="space-y-4">
                <div>
                  <h3 className="mb-2 font-medium">Provider Card</h3>
                  <MatchProviderCard />
                </div>
                <div>
                  <h3 className="mb-2 font-medium">Job Card</h3>
                  <MatchJobCard
                    onViewDetails={() => alert("View job details")}
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h3 className="mb-2 font-medium">Rate Insights</h3>
                  <MatchRateInsights />
                </div>
                <div>
                  <h3 className="mb-2 font-medium">Negotiation Panel</h3>
                  <MatchNegotiationPanel className="max-h-96" />
                </div>
              </div>
            </div>
          </MatchProvider>
        )}
      </Card>

      <Card className="p-4">
        <h3 className="mb-2 font-semibold">Usage Examples</h3>
        <div className="space-y-2 text-sm">
          <div>
            <strong>Full Widget:</strong> Use <code>MatchWidget</code> for
            complete match management pages
          </div>
          <div>
            <strong>Rate Only:</strong> Use <code>MatchRateNegotiation</code>{" "}
            for focused rate negotiation flows
          </div>
          <div>
            <strong>Individual Components:</strong> Use individual components
            with <code>MatchProvider</code> for custom layouts
          </div>
          <div>
            <strong>Data/Presentation Separation:</strong> Context handles all
            data and actions, components focus on presentation
          </div>
        </div>
      </Card>
    </div>
  );
}
