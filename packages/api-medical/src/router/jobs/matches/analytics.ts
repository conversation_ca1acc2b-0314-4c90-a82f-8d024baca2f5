import { z } from "zod";

import type { <PERSON>risma } from "@axa/database-medical";
import {
  MatchInitiator,
  MatchStatus,
  PayType,
  StepStatus,
  StepType,
} from "@axa/database-medical";

import { createTRPCRouter, protectedProcedure } from "../../../trpc";

export const matchesAnalyticsRouter = createTRPCRouter({
  // Dashboard overview for matches
  dashboard: protectedProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        organizationId: z.string().optional(),
        providerId: z.string().optional(),
        status: z.nativeEnum(MatchStatus).optional(),
        initiator: z.nativeEnum(MatchInitiator).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Set date range (default to last 30 days)
      const endDate = input.endDate ?? new Date();
      const startDate = input.startDate ?? new Date(endDate);
      startDate.setDate(startDate.getDate() - 30);

      // Build where clause for matches
      const matchWhere: Prisma.MatchWhereInput = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        ...(input.organizationId && { organizationId: input.organizationId }),
        ...(input.providerId && { providerId: input.providerId }),
        ...(input.status && { status: input.status }),
        ...(input.initiator && { initiator: input.initiator }),
      };

      // Get matches by status
      const matchesByStatus = (
        await ctx.prisma.match.groupBy({
          by: ["status"],
          _count: true,
          where: matchWhere,
        })
      ).reduce(
        (acc, match) => {
          acc[match.status] = match._count;
          return acc;
        },
        {
          [MatchStatus.PENDING]: 0,
          [MatchStatus.VALIDATING]: 0,
          [MatchStatus.NEGOTIATING]: 0,
          [MatchStatus.FINALIZING]: 0,
          [MatchStatus.MATCHED]: 0,
          [MatchStatus.WITHDRAWN]: 0,
          [MatchStatus.DECLINED]: 0,
          [MatchStatus.EXPIRED]: 0,
          [MatchStatus.CANCELLED]: 0,
        } as Record<MatchStatus, number>,
      );

      // Get matches by initiator
      const matchesByInitiator = (
        await ctx.prisma.match.groupBy({
          by: ["initiator"],
          _count: true,
          where: matchWhere,
        })
      ).reduce(
        (acc, match) => {
          acc[match.initiator] = match._count;
          return acc;
        },
        {
          [MatchInitiator.PROVIDER]: 0,
          [MatchInitiator.ORGANIZATION]: 0,
          [MatchInitiator.MUTUAL]: 0,
          [MatchInitiator.REFERRAL]: 0,
        } as Record<MatchInitiator, number>,
      );

      // Get rate negotiation steps by status
      const negotiationSteps = await ctx.prisma.matchStep.groupBy({
        by: ["status"],
        _count: true,
        where: {
          type: StepType.RATE_NEGOTIATION,
          match: matchWhere,
        },
      });

      const negotiationsByStatus = negotiationSteps.reduce(
        (acc, step) => {
          acc[step.status] = step._count;
          return acc;
        },
        {
          [StepStatus.PENDING]: 0,
          [StepStatus.VALIDATING]: 0,
          [StepStatus.IN_PROGRESS]: 0,
          [StepStatus.COMPLETED]: 0,
          [StepStatus.SKIPPED]: 0,
          [StepStatus.FAILED]: 0,
          [StepStatus.CANCELLED]: 0,
        } as Record<StepStatus, number>,
      );

      // Recent matches for timeline
      const recentMatches = await ctx.prisma.match.findMany({
        where: matchWhere,
        orderBy: { createdAt: "desc" },
        take: 10,
        include: {
          job: {
            select: {
              id: true,
              summary: true,
              role: true,
              paymentRate: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          provider: {
            select: {
              id: true,
              title: true,
              person: {
                select: {
                  firstName: true,
                  lastName: true,
                  avatar: true,
                },
              },
            },
          },
          compensation: {
            select: {
              currentOfferRate: true,
              finalAgreedRate: true,
              negotiationStatus: true,
            },
          },
        },
      });

      // Summary statistics
      const totalMatches = Object.values(matchesByStatus).reduce(
        (sum, count) => sum + count,
        0,
      );
      const completedMatches = matchesByStatus[MatchStatus.MATCHED];
      const successRate =
        totalMatches > 0 ? (completedMatches / totalMatches) * 100 : 0;

      return {
        matchesByStatus,
        matchesByInitiator,
        negotiationsByStatus,
        recentMatches,
        summary: {
          totalMatches,
          completedMatches,
          pendingMatches: matchesByStatus[MatchStatus.PENDING],
          negotiatingMatches: matchesByStatus[MatchStatus.NEGOTIATING],
          successRate: Math.round(successRate * 100) / 100,
          averageTimeToMatch: 0, // TODO: Calculate based on created vs matched dates
        },
      };
    }),

  // Rate negotiation analytics
  compensationAnalytics: protectedProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        organizationId: z.string().optional(),
        providerId: z.string().optional(),
        rateStrategy: z.string().optional(),
        minRate: z.number().optional(),
        maxRate: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const endDate = input.endDate ?? new Date();
      const startDate = input.startDate ?? new Date(endDate);
      startDate.setDate(startDate.getDate() - 30);

      const compensationWhere: Prisma.JobCompensationWhereInput = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        ...(input.organizationId && { organizationId: input.organizationId }),
        ...(input.providerId && { providerId: input.providerId }),
        ...(input.rateStrategy && { rateStrategy: input.rateStrategy }),
        ...(input.minRate && { minRate: { gte: input.minRate } }),
        ...(input.maxRate && { maxRate: { lte: input.maxRate } }),
      };

      // Rate statistics
      const rateStats = await ctx.prisma.jobCompensation.aggregate({
        _avg: {
          minRate: true,
          maxRate: true,
          currentOfferRate: true,
          finalAgreedRate: true,
          negotiationCount: true,
        },
        _min: {
          minRate: true,
          maxRate: true,
          currentOfferRate: true,
          finalAgreedRate: true,
        },
        _max: {
          minRate: true,
          maxRate: true,
          currentOfferRate: true,
          finalAgreedRate: true,
        },
        _count: {
          finalAgreedRate: true,
        },
        where: compensationWhere,
      });

      // Compensation by strategy
      const compensationByStrategy = await ctx.prisma.jobCompensation.groupBy({
        by: ["rateStrategy"],
        _count: true,
        _avg: {
          finalAgreedRate: true,
          negotiationCount: true,
        },
        where: compensationWhere,
      });

      // Compensation by payment type
      const compensationByPaymentType =
        await ctx.prisma.jobCompensation.groupBy({
          by: ["paymentType"],
          _count: true,
          _avg: {
            finalAgreedRate: true,
          },
          where: compensationWhere,
        });

      // Rate comparison data
      const rateComparisons = await ctx.prisma.jobCompensation.findMany({
        where: {
          ...compensationWhere,
          finalAgreedRate: { not: null },
        },
        select: {
          minRate: true,
          maxRate: true,
          currentOfferRate: true,
          finalAgreedRate: true,
          rateStrategy: true,
          negotiationCount: true,
          job: {
            select: {
              role: true,
              paymentRate: true,
            },
          },
          match: {
            select: {
              initiator: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 50,
      });

      return {
        rateStatistics: {
          avgMinRate: rateStats._avg.minRate ?? 0,
          avgMaxRate: rateStats._avg.maxRate ?? 0,
          avgCurrentOffer: rateStats._avg.currentOfferRate ?? 0,
          avgFinalRate: rateStats._avg.finalAgreedRate ?? 0,
          avgNegotiationRounds: rateStats._avg.negotiationCount ?? 0,
          minRateRange: {
            min: rateStats._min.minRate ?? 0,
            max: rateStats._max.minRate ?? 0,
          },
          maxRateRange: {
            min: rateStats._min.maxRate ?? 0,
            max: rateStats._max.maxRate ?? 0,
          },
          finalizedCount: rateStats._count.finalAgreedRate,
        },
        compensationByStrategy: compensationByStrategy.map((item) => ({
          strategy: item.rateStrategy ?? "unknown",
          count: item._count,
          avgFinalRate: item._avg.finalAgreedRate ?? 0,
          avgNegotiationRounds: item._avg.negotiationCount ?? 0,
        })),
        compensationByPaymentType: compensationByPaymentType.map((item) => ({
          paymentType: item.paymentType,
          count: item._count,
          avgFinalRate: item._avg.finalAgreedRate ?? 0,
        })),
        rateComparisons,
      };
    }),

  // Rate benchmarking for organizations
  rateBenchmarking: protectedProcedure
    .input(
      z.object({
        role: z.string(),
        organizationId: z.string().optional(),
        location: z.string().optional(),
        paymentType: z.nativeEnum(PayType).optional().default(PayType.HOURLY),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Get compensation data for similar roles
      const compensationData = await ctx.prisma.jobCompensation.findMany({
        where: {
          job: {
            role: {
              contains: input.role,
              mode: "insensitive",
            },
          },
          paymentType: input.paymentType,
          finalAgreedRate: { not: null },
          ...(input.organizationId && {
            organizationId: { not: input.organizationId }, // Exclude current org for comparison
          }),
        },
        select: {
          minRate: true,
          maxRate: true,
          finalAgreedRate: true,
          rateStrategy: true,
          job: {
            select: {
              role: true,
              paymentRate: true,
              organization: {
                select: {
                  name: true,
                  type: true,
                },
              },
              location: {
                select: {
                  name: true,
                  address: {
                    select: {
                      state: true,
                      city: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 100,
      });

      // Calculate benchmarks
      const rates = compensationData
        .map((c) => c.finalAgreedRate!)
        .filter(Boolean);
      rates.sort((a, b) => a - b);

      const percentile = (arr: number[], p: number): number => {
        if (arr.length === 0) return 0;
        const index = (p / 100) * (arr.length - 1);
        const lower = Math.floor(index);
        const upper = Math.ceil(index);
        const weight = index % 1;
        return (arr[lower] ?? 0) * (1 - weight) + (arr[upper] ?? 0) * weight;
      };

      const benchmarks =
        rates.length > 0
          ? {
              p10: percentile(rates, 10),
              p25: percentile(rates, 25),
              p50: percentile(rates, 50),
              p75: percentile(rates, 75),
              p90: percentile(rates, 90),
              min: Math.min(...rates),
              max: Math.max(...rates),
              avg: rates.reduce((sum, rate) => sum + rate, 0) / rates.length,
              count: rates.length,
            }
          : null;

      return {
        role: input.role,
        paymentType: input.paymentType,
        benchmarks,
        sampleData: compensationData.slice(0, 10), // Return top 10 for reference
      };
    }),

  // Match timeline and trends
  matchTrends: protectedProcedure
    .input(
      z.object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        groupBy: z.enum(["day", "week", "month"]).optional().default("day"),
        organizationId: z.string().optional(),
        providerId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const endDate = input.endDate ?? new Date();
      const startDate = input.startDate ?? new Date(endDate);
      startDate.setDate(startDate.getDate() - 30);

      const matchWhere: Prisma.MatchWhereInput = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        ...(input.organizationId && { organizationId: input.organizationId }),
        ...(input.providerId && { providerId: input.providerId }),
      };

      // Get matches with dates for grouping
      const matches = await ctx.prisma.match.findMany({
        where: matchWhere,
        select: {
          id: true,
          status: true,
          initiator: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Group matches by time period
      const groupedMatches: Record<
        string,
        {
          total: number;
          byStatus: Record<MatchStatus, number>;
          byInitiator: Record<MatchInitiator, number>;
        }
      > = {};

      matches.forEach((match) => {
        let key: string;
        const date = new Date(match.createdAt);

        switch (input.groupBy) {
          case "week": {
            const week = Math.ceil(date.getDate() / 7);
            key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-W${week}`;
            break;
          }
          case "month": {
            key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}`;
            break;
          }
          default: {
            // day
            key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
            break;
          }
        }

        if (!groupedMatches[key]) {
          groupedMatches[key] = {
            total: 0,
            byStatus: {
              [MatchStatus.PENDING]: 0,
              [MatchStatus.VALIDATING]: 0,
              [MatchStatus.NEGOTIATING]: 0,
              [MatchStatus.FINALIZING]: 0,
              [MatchStatus.MATCHED]: 0,
              [MatchStatus.WITHDRAWN]: 0,
              [MatchStatus.DECLINED]: 0,
              [MatchStatus.EXPIRED]: 0,
              [MatchStatus.CANCELLED]: 0,
            },
            byInitiator: {
              [MatchInitiator.PROVIDER]: 0,
              [MatchInitiator.ORGANIZATION]: 0,
              [MatchInitiator.MUTUAL]: 0,
              [MatchInitiator.REFERRAL]: 0,
            },
          };
        }

        groupedMatches[key]!.total++;
        groupedMatches[key]!.byStatus[match.status]++;
        groupedMatches[key]!.byInitiator[match.initiator]++;
      });

      return {
        groupBy: input.groupBy,
        dateRange: { startDate, endDate },
        trends: Object.entries(groupedMatches)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([period, data]) => ({
            period,
            ...data,
          })),
      };
    }),

  // Simple procedure to get match overview stats
  matchOverview: protectedProcedure.query(async ({ ctx }) => {
    const totalMatches = await ctx.prisma.match.count();
    const activeMatches = await ctx.prisma.match.count({
      where: {
        status: {
          in: [
            MatchStatus.NEGOTIATING,
            MatchStatus.VALIDATING,
            MatchStatus.FINALIZING,
          ],
        },
      },
    });
    const completedMatches = await ctx.prisma.match.count({
      where: { status: MatchStatus.MATCHED },
    });

    return {
      totalMatches,
      activeMatches,
      completedMatches,
      successRate:
        totalMatches > 0 ? (completedMatches / totalMatches) * 100 : 0,
    };
  }),

  // Simple compensation overview
  compensationOverview: protectedProcedure.query(async ({ ctx }) => {
    const rateStats = await ctx.prisma.jobCompensation.aggregate({
      _avg: {
        minRate: true,
        maxRate: true,
        finalAgreedRate: true,
      },
      _count: {
        finalAgreedRate: true,
      },
    });

    const totalNegotiations = await ctx.prisma.jobCompensation.count();
    const finalizedNegotiations = rateStats._count.finalAgreedRate;

    return {
      totalNegotiations,
      finalizedNegotiations,
      averageMinRate: rateStats._avg.minRate ?? 0,
      averageMaxRate: rateStats._avg.maxRate ?? 0,
      averageFinalRate: rateStats._avg.finalAgreedRate ?? 0,
      finalizationRate:
        totalNegotiations > 0
          ? (finalizedNegotiations / totalNegotiations) * 100
          : 0,
    };
  }),
});
