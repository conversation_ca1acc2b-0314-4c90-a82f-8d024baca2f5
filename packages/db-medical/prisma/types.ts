// =====================================================
// RATE NEGOTIATION METADATA TYPES
// =====================================================

/**
 * Individual rate offer within negotiation
 */
export interface RateOffer {
  id: string;
  proposedRate: number;
  madeBy: "PROVIDER" | "ORGANIZATION";
  madeAt: string; // ISO datetime
  message?: string;
  expiresAt?: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED" | "EXPIRED" | "WITHDRAWN";
  declineReason?: string;
  respondedAt?: string;
}

/**
 * Rate negotiation state stored in MatchStep.metadata
 */
export interface RateNegotiationMetadata {
  // Current negotiation state
  currentOfferRate: number;
  lastOfferBy: "PROVIDER" | "ORGANIZATION";
  lastOfferAt: string; // ISO datetime
  offerExpiresAt?: string; // ISO datetime

  // Offer history (application-level tracking)
  offerHistory: RateOffer[];

  // Negotiation configuration
  strategy: "conservative" | "competitive" | "balanced" | "premium";
  allowCounterOffers: boolean;
  maxNegotiationRounds: number;
  currentRound: number;

  // Integration points
  threadId?: string; // For messaging
  compensationId: string; // JobCompensation record
}

// =====================================================
// IDENTITY VERIFICATION METADATA TYPES
// =====================================================

export interface IdentityVerificationMetadata {
  // Verification details
  verificationType: "GOVERNMENT_ID" | "PASSPORT" | "DRIVER_LICENSE";
  documentNumber?: string;
  documentType?: string;
  verificationStatus: "PENDING" | "IN_PROGRESS" | "VERIFIED" | "REJECTED";

  // External service integration
  serviceProvider?: "JUMIO" | "ONFIDO" | "ID_ME" | "MANUAL";
  externalReferenceId?: string;

  // Verification results
  confidence?: number; // 0-100
  rejectionReason?: string;
  verifiedAt?: string; // ISO datetime

  // Document metadata
  documentImages?: {
    front?: string; // URL or base64
    back?: string;
    selfie?: string;
  };
}

// =====================================================
// BACKGROUND CHECK METADATA TYPES
// =====================================================

export interface BackgroundCheckMetadata {
  // Check configuration
  checkTypes: (
    | "CRIMINAL"
    | "EMPLOYMENT"
    | "EDUCATION"
    | "REFERENCE"
    | "CREDIT"
  )[];
  scope: "NATIONAL" | "STATE" | "COUNTY" | "INTERNATIONAL";

  // Service integration
  serviceProvider?: "CHECKR" | "STERLING" | "HireRight" | "ACCURATE" | "MANUAL";
  externalReferenceId?: string;
  packageId?: string;

  // Progress tracking
  status:
    | "PENDING"
    | "IN_PROGRESS"
    | "COMPLETED"
    | "ADVERSE_ACTION"
    | "DISPUTED";
  completedChecks: string[];
  pendingChecks: string[];

  // Results summary
  overallResult?: "CLEAR" | "CONSIDER" | "SUSPENDED";
  findings?: {
    type: string;
    severity: "LOW" | "MEDIUM" | "HIGH";
    description: string;
    county?: string;
    date?: string;
  }[];

  // Timeline
  initiatedAt?: string; // ISO datetime
  estimatedCompletionAt?: string;
  completedAt?: string;
}

// =====================================================
// INTERVIEW METADATA TYPES
// =====================================================

export interface InterviewMetadata {
  // Interview scheduling
  scheduledAt?: string; // ISO datetime
  duration?: number; // minutes
  timezone?: string;

  // Interview format
  type: "PHONE" | "VIDEO" | "IN_PERSON" | "PANEL";
  platform?: "ZOOM" | "TEAMS" | "GOOGLE_MEET" | "CUSTOM";
  meetingLink?: string;
  meetingId?: string;

  // Participants
  interviewers: {
    id: string;
    name: string;
    role: string;
    email?: string;
  }[];

  // Interview content
  questions?: {
    id: string;
    question: string;
    category: "TECHNICAL" | "BEHAVIORAL" | "SITUATIONAL" | "CULTURAL";
    response?: string;
    rating?: number; // 1-5
    notes?: string;
  }[];

  // Results
  overallRating?: number; // 1-5
  recommendation: "HIRE" | "NO_HIRE" | "MAYBE" | "PENDING";
  feedback?: string;
  strengths?: string[];
  concerns?: string[];
}

// =====================================================
// CONTRACT TERMS METADATA TYPES
// =====================================================

export interface ContractTermsMetadata {
  // Contract details
  contractType: "W2" | "1099" | "CORP_TO_CORP" | "TEMP_TO_PERM";
  startDate?: string; // ISO date
  endDate?: string;
  renewalTerms?: string;

  // Work arrangement
  workLocation: "ON_SITE" | "REMOTE" | "HYBRID";
  workSchedule?: {
    hoursPerWeek: number;
    daysPerWeek: number;
    shiftType: "DAYS" | "EVENINGS" | "NIGHTS" | "ROTATING";
  };

  // Compensation (references JobCompensation)
  compensationId: string;
  additionalBenefits?: string[];

  // Terms negotiation
  negotiableTerms: string[];
  agreedTerms: Record<string, unknown>;
  pendingTerms: Record<string, unknown>;

  // Legal
  governingLaw?: string;
  disputeResolution?: string;
  terminationClause?: string;

  // Document management
  contractTemplateId?: string;
  draftVersions: {
    version: number;
    createdAt: string;
    createdBy: string;
    changes: string[];
  }[];
}

// =====================================================
// SCHEDULE NEGOTIATION METADATA TYPES
// =====================================================

export interface ScheduleNegotiationMetadata {
  // Schedule preferences
  preferredStartDate?: string; // ISO date
  availableDays: (
    | "MONDAY"
    | "TUESDAY"
    | "WEDNESDAY"
    | "THURSDAY"
    | "FRIDAY"
    | "SATURDAY"
    | "SUNDAY"
  )[];
  preferredShifts: ("MORNING" | "AFTERNOON" | "EVENING" | "NIGHT")[];

  // Flexibility
  isFlexible: boolean;
  blackoutDates?: string[]; // ISO dates
  vacationRequests?: {
    startDate: string;
    endDate: string;
    reason?: string;
    approved?: boolean;
  }[];

  // Negotiation state
  proposedSchedules: {
    id: string;
    proposedBy: "PROVIDER" | "ORGANIZATION";
    schedule: {
      startDate: string;
      pattern: "WEEKLY" | "BIWEEKLY" | "MONTHLY" | "CUSTOM";
      shifts: {
        day: string;
        startTime: string;
        endTime: string;
        break?: number; // minutes
      }[];
    };
    status: "PENDING" | "ACCEPTED" | "REJECTED" | "COUNTERED";
    proposedAt: string;
    respondedAt?: string;
  }[];

  // Final agreement
  agreedSchedule?: {
    startDate: string;
    pattern: string;
    shifts: unknown[];
  };
}

export type MatchStepMetadata =
  | RateNegotiationMetadata
  | IdentityVerificationMetadata
  | BackgroundCheckMetadata
  | InterviewMetadata
  | ContractTermsMetadata
  | ScheduleNegotiationMetadata;
