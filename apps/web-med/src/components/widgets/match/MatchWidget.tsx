"use client";

import { MatchCockpit } from "./components/match-cockpit";
import { MatchProvider } from "./context/MatchContext";

export interface MatchWidgetProps {
  matchId: string;
  onViewJobDetails?: () => void;
  showNegotiationHeader?: boolean;
  defaultNegotiationExpanded?: boolean;
  showMessageInput?: boolean;
  className?: string;
}

export function MatchWidget({
  matchId,
  onViewJobDetails,
  showNegotiationHeader = true,
  defaultNegotiationExpanded = true,
  showMessageInput = true,
  className,
}: MatchWidgetProps) {
  return (
    <MatchProvider matchId={matchId}>
      <MatchCockpit
        className={className}
        onViewJobDetails={onViewJobDetails}
        showNegotiationHeader={showNegotiationHeader}
        defaultNegotiationExpanded={defaultNegotiationExpanded}
        showMessageInput={showMessageInput}
      />
    </MatchProvider>
  );
}
