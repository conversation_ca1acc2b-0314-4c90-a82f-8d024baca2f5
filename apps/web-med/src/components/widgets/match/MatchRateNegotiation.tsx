"use client";

import { useMemo } from "react";

import type { RouterOutputs } from "@/api";
import { api } from "@/api/client";

import { NegotiationPanel } from "@axa/ui/blocks/negotiation-center";
import type { Milestone, Registry } from "@axa/ui/blocks/negotiation-timeline";
import {
  MessageBlock,
  RateOfferBlock,
  RateNegotiatedBlock,
} from "@axa/ui/blocks/negotiation-timeline";

// Registry for rate negotiation blocks
const rateRegistry: Registry = {
  message: MessageBlock,
  "rate-offer": RateOfferBlock,
  "rate-negotiated": RateNegotiatedBlock,
};

export interface MatchRateNegotiationProps {
  matchId: string;
  loading?: boolean;
}

export function MatchRateNegotiation({
  matchId,
  loading = false,
}: MatchRateNegotiationProps) {
  // Fetch match data
  const { data: match, isLoading } = api.matches.matches.get.useQuery(
    { 
      id: matchId,
      include: {
        job: true,
        provider: true,
        organization: true,
        compensation: true,
        steps: true,
      },
    },
    { enabled: !!matchId }
  );

  // Rate negotiation mutations
  const submitOfferMutation = api.matches.rates.submitOffer.useMutation();
  const respondToOfferMutation = api.matches.rates.respondToOffer.useMutation();

  // Transform match data into rate-focused milestones
  const milestones = useMemo((): Milestone[] => {
    if (!match) return [];

    const rateBlocks = [];

    // Current active rate offer
    if (match.compensation?.currentOfferRate) {
      const rate = match.compensation.currentOfferRate;
      const isProvider = match.compensation.lastOfferBy === match.providerId;
      const isPending = match.compensation.negotiationStatus === "ACTIVE";

      rateBlocks.push({
        id: `current-offer-${match.compensation.id}`,
        type: "rate-offer" as const,
        timestamp: new Date(match.compensation.updatedAt || match.createdAt),
        data: {
          rate: `$${rate}/hr`,
          status: isPending ? ("pending" as const) : ("accepted" as const),
          expectedParty: isPending ? (isProvider ? "organization" : "provider") : undefined,
          onAccept: isPending ? () => handleAcceptRate(rate) : undefined,
          onCounter: isPending ? () => handleCounterRate() : undefined,
        },
      });
    }

    // Final agreed rate
    if (match.compensation?.finalAgreedRate) {
      const providerName = `${match.provider.person.firstName} ${match.provider.person.lastName}`;
      
      rateBlocks.push({
        id: `final-rate-${match.compensation.id}`,
        type: "rate-negotiated" as const,
        timestamp: new Date(match.compensation.updatedAt || match.createdAt),
        data: {
          doctor: providerName,
          rate: `$${match.compensation.finalAgreedRate}/hr`,
          message: "Rate successfully negotiated and agreed upon.",
        },
      });
    }

    return [{
      id: "rate-negotiation",
      title: "Rate Negotiation",
      status: match.status === "MATCHED" ? "completed" : "active",
      timestamp: new Date(match.createdAt),
      blocks: rateBlocks,
    }];
  }, [match]);

  const handleAcceptRate = async (rate: number) => {
    if (!match) return;

    try {
      // Find the current offer ID from compensation metadata
      const compensation = match.compensation;
      if (!compensation) return;

      await respondToOfferMutation.mutateAsync({
        matchId: match.id,
        offerId: "current", // TODO: Get actual offer ID from metadata
        response: "ACCEPT",
      });
    } catch (error) {
      console.error("Failed to accept rate:", error);
    }
  };

  const handleCounterRate = () => {
    if (!match?.compensation) return;

    const currentRate = match.compensation.currentOfferRate;
    const counterRate = prompt(`Enter counter offer (current: $${currentRate}/hr):`);
    
    if (counterRate && !isNaN(parseFloat(counterRate))) {
      handleSubmitOffer(parseFloat(counterRate));
    }
  };

  const handleSubmitOffer = async (rate: number, message?: string) => {
    if (!match) return;

    try {
      await submitOfferMutation.mutateAsync({
        matchId: match.id,
        proposedRate: rate,
        message,
      });
    } catch (error) {
      console.error("Failed to submit offer:", error);
    }
  };

  const handleSendMessage = async (message: string) => {
    // TODO: Implement message sending
    console.log("Send message:", message);
  };

  return (
    <NegotiationPanel
      milestones={milestones}
      registry={rateRegistry}
      defaultExpanded={true}
      showMessageInput={true}
      onSendMessage={handleSendMessage}
      loading={loading || isLoading}
    />
  );
}

export type { MatchRateNegotiationProps };
