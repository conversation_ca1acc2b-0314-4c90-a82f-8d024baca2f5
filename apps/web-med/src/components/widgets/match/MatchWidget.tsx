"use client";

import { useMemo } from "react";

import type {
  MessageBlockData,
  Milestone,
  Registry,
  RegistryItem,
} from "@axa/ui/blocks/negotiation-timeline";
import { CockpitLayout } from "@axa/ui/blocks/negotiation-center";
import {
  MessageBlock,
  RateNegotiatedBlock,
  RateOfferBlock,
} from "@axa/ui/blocks/negotiation-timeline";

import type { RouterOutputs } from "@/api";

import { useMatchTimeline } from "./hooks/useMatchTimeline";

// Registry for timeline block types
const timelineRegistry: Registry = {
  message: {
    type: "message",
    render: (block, loading) => (
      <MessageBlock data={block.data as MessageBlockData} loading={loading} />
    ),
  },
  "rate-offer": {
    type: "rate-offer",
    render: (block, loading) => (
      <RateOfferBlock
        data={block.data as RateOfferBlockData}
        loading={loading}
      />
    ),
  },
  "rate-negotiated": {
    type: "rate-negotiated",
    render: (block, loading) => (
      <RateNegotiatedBlock
        data={block.data as RateNegotiatedBlockData}
        loading={loading}
      />
    ),
  },
};

export interface MatchWidgetProps {
  match: RouterOutputs["jobs"]["matches"]["get"];
  onSendMessage?: (message: string) => void;
  onAcceptRate?: (rate: number) => void;
  onCounterRate?: (rate: number, message?: string) => void;
  loading?: boolean;
}

export function MatchWidget({
  match,
  onSendMessage,
  onAcceptRate,
  onCounterRate,
  loading = false,
}: MatchWidgetProps) {
  // Transform match data into timeline milestones
  const milestones = useMatchTimeline({
    match,
    onAcceptRate,
    onCounterRate,
  });

  // Provider props for the cockpit
  const providerProps = useMemo(() => {
    if (!match?.provider) return null;

    return {
      name: `${match.provider.person.firstName} ${match.provider.person.lastName}`,
      avatar: match.provider.person.avatar || "",
      specialty: match.provider.title || "Healthcare Provider",
      experience: "5+ years", // TODO: Calculate from provider data
      authorized: true,
    };
  }, [match?.provider]);

  // Job props for the cockpit
  const jobProps = useMemo(() => {
    if (!match?.job) return null;

    return {
      specialty: match.job.role,
      schedule: "Full-time", // TODO: Get from job schedule
      jobType: match.job.paymentType,
      salary: match.job.paymentAmount
        ? `$${match.job.paymentAmount}`
        : undefined,
      organization: match.organization
        ? {
            id: match.organization.id,
            name: match.organization.name,
            avatar: match.organization.avatar,
          }
        : undefined,
    };
  }, [match?.job, match?.organization]);

  // Rate insights props
  const rateInsightsProps = useMemo(() => {
    const currentRate =
      match?.compensation?.currentOfferRate || match?.job?.paymentAmount || 0;

    return {
      currentRate,
      currency: "$",
      unit: "per hour",
      // TODO: Add real market data
      marketComparison: {
        percentile: 65,
        difference: 10,
        trend: "above" as const,
      },
      demandMetrics: {
        level: "medium" as const,
        score: 65,
        trend: "stable" as const,
      },
    };
  }, [match?.compensation, match?.job]);

  // Negotiation props
  const negotiationProps = useMemo(
    () => ({
      milestones,
      registry: timelineRegistry,
      defaultExpanded: true,
      showMessageInput: true,
      onSendMessage,
    }),
    [milestones, onSendMessage],
  );

  if (!providerProps || !jobProps) {
    return <div>Loading match data...</div>;
  }

  return (
    <CockpitLayout
      providerProps={providerProps}
      jobProps={jobProps}
      rateInsightsProps={rateInsightsProps}
      negotiationProps={negotiationProps}
    />
  );
}
