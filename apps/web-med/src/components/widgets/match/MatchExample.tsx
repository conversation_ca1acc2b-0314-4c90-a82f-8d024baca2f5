"use client";

import { useState } from "react";

import { <PERSON><PERSON> } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";

import { MatchWidget } from "./MatchWidget";
import { MatchRateNegotiation } from "./MatchRateNegotiation";

// Mock match data for demonstration
const mockMatch = {
  id: "match-123",
  status: "NEGOTIATING" as const,
  initiator: "PROVIDER" as const,
  initiationNote: "I'm interested in this position and would like to discuss the rate.",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  jobId: "job-456",
  providerId: "provider-789",
  organizationId: "org-101",
  
  job: {
    id: "job-456",
    status: "PUBLISHED" as const,
    summary: "Emergency Department Physician",
    role: "Emergency Medicine",
    paymentType: "HOURLY" as const,
    paymentAmount: 150,
    allowRateNegotiation: true,
    minNegotiableRate: 120,
    maxNegotiableRate: 200,
  },
  
  provider: {
    id: "provider-789",
    title: "Emergency Medicine Physician",
    person: {
      id: "person-111",
      firstName: "Dr. <PERSON>",
      lastName: "<PERSON>",
      avatar: null,
    },
  },
  
  organization: {
    id: "org-101",
    name: "City General Hospital",
    avatar: null,
  },
  
  compensation: {
    id: "comp-222",
    minRate: 120,
    maxRate: 200,
    currentOfferRate: 165,
    finalAgreedRate: null,
    rateStrategy: "balanced",
    negotiationCount: 2,
    negotiationStatus: "ACTIVE",
    lastOfferBy: "org-101",
    offerExpiresAt: null,
    paymentType: "HOURLY" as const,
    updatedAt: new Date().toISOString(),
  },
};

export function MatchExample() {
  const [view, setView] = useState<"full" | "rate-only">("full");
  const [loading, setLoading] = useState(false);

  const handleAcceptRate = async (rate: number) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert(`Rate $${rate}/hr accepted!`);
    setLoading(false);
  };

  const handleCounterRate = async (rate: number, message?: string) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert(`Counter offer submitted: $${rate}/hr${message ? ` - ${message}` : ""}`);
    setLoading(false);
  };

  const handleSendMessage = async (message: string) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    alert(`Message sent: ${message}`);
    setLoading(false);
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Match Widget Examples</h1>
        <div className="flex gap-2">
          <Button
            variant={view === "full" ? "primary" : "outline"}
            onClick={() => setView("full")}
          >
            Full Match Widget
          </Button>
          <Button
            variant={view === "rate-only" ? "primary" : "outline"}
            onClick={() => setView("rate-only")}
          >
            Rate Negotiation Only
          </Button>
        </div>
      </div>

      <Card className="p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">
            {view === "full" ? "Complete Match Cockpit" : "Rate Negotiation Panel"}
          </h2>
          <p className="text-sm text-muted-foreground">
            {view === "full" 
              ? "Full match interface with provider info, job details, rate insights, and negotiation timeline"
              : "Focused rate negotiation interface using the negotiation timeline"
            }
          </p>
        </div>

        {view === "full" ? (
          <MatchWidget
            match={mockMatch}
            onSendMessage={handleSendMessage}
            onAcceptRate={handleAcceptRate}
            onCounterRate={handleCounterRate}
            loading={loading}
          />
        ) : (
          <div className="max-w-2xl">
            <MatchRateNegotiation
              matchId={mockMatch.id}
              loading={loading}
            />
          </div>
        )}
      </Card>

      <Card className="p-4">
        <h3 className="font-semibold mb-2">Usage Examples</h3>
        <div className="space-y-2 text-sm">
          <div>
            <strong>Full Widget:</strong> Use <code>MatchWidget</code> for complete match management pages
          </div>
          <div>
            <strong>Rate Only:</strong> Use <code>MatchRateNegotiation</code> for focused rate negotiation flows
          </div>
          <div>
            <strong>Composable:</strong> Both components use the same negotiation timeline blocks for consistency
          </div>
        </div>
      </Card>
    </div>
  );
}
