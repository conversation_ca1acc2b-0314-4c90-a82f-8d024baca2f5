# Migration: Application/Offer Process to Matches API

## Overview

This document outlines the migration from the current provider application and organization offer process to the new Matches API. The Matches API will take over responsibility for creating job positions and managing the entire provider-organization matching workflow.

## Current State Analysis

### Current Application/Offer Flow

1. **Provider Applications**: Providers apply to published job posts
2. **Organization Offers**: Organizations send offers directly to providers
3. **Acceptance Process**: Applications/offers are accepted, creating JobPositions
4. **Position Creation**: Accepted applications/offers trigger JobPosition creation
5. **Shift Generation**: JobPositions with ACTIVE status generate shifts automatically

### Current API Endpoints

#### Applications

- `api.applications.provider.create` - Provider creates application
- `api.applications.organization.approve` - Organization accepts application
- `api.applications.organization.reject` - Organization rejects application
- `api.applications.getMany` - List applications with filters
- `api.applications.get` - Get single application

#### Offers

- `api.offers.organization.send` - Organization creates offer
- `api.offers.provider.accept` - Provider accepts offer
- `api.offers.provider.reject` - Provider rejects offer
- `api.offers.getMany` - List offers with filters
- `api.offers.get` - Get single offer

### Current Database Models

```prisma
model Application {
  id             String            @id @default(cuid())
  status         ApplicationStatus @default(PENDING)
  notes          String?
  jobId          String
  organizationId String
  providerId     String
  positionId     String?           @unique
  // ... timestamps and relations
}

model Offer {
  id             String      @id @default(cuid())
  status         OfferStatus @default(PENDING)
  notes          String?
  jobId          String
  organizationId String
  providerId     String
  positionId     String?     @unique
  // ... timestamps and relations
}
```

## Target State: Matches API

### New Match-Centric Flow

1. **Match Creation**: Replace applications/offers with matches
2. **Match Steps**: Use structured workflow with steps (rate negotiation, verification, etc.)
3. **Position Creation**: Matches API creates JobPositions when match reaches MATCHED status
4. **Workflow Management**: Structured step-by-step process with clear status transitions

### Existing Matches API Structure

```typescript
// Match statuses
enum MatchStatus {
  PENDING,
  ACCEPTED,
  DECLINED,
  VALIDATING,
  NEGOTIATING,
  FINALIZING,
  MATCHED,
  WITHDRAWN,
  CANCELLED,
  EXPIRED,
}

// Match steps for workflow
enum StepType {
  IDENTITY_VERIFICATION,
  BACKGROUND_CHECK,
  RATE_NEGOTIATION,
  INTERVIEW,
  SCHEDULE_NEGOTIATION,
  BENEFITS_NEGOTIATION,
  CONTRACT_TERMS,
  REFERENCE_CHECK,
  SKILLS_ASSESSMENT,
  DRUG_SCREENING,
  CREDENTIAL_VERIFICATION,
  FINAL_APPROVAL,
  ONBOARDING_PREP,
  EQUIPMENT_ASSIGNMENT,
  ORIENTATION_SCHEDULING,
}
```

### Enhanced Matches API Requirements

The Matches API needs to be enhanced to handle position creation:

```typescript
// New endpoint needed
api.matches.finalizeMatch({
  matchId: string,
  finalTerms: {
    paymentType: PayType,
    paymentAmount: number,
    paymentRate: number,
    // ... other position fields
  }
}) -> { positionId: string }
```

## Migration Plan

### Phase 1: Enhance Matches API for Position Creation

#### 1.1 Add Position Creation Logic to Matches

**File**: `packages/api-medical/src/router/jobs/matches/matches.ts`

Add new mutation for finalizing matches and creating positions:

```typescript
finalizeMatch: authorizedProcedure
  .input(
    z.object({
      matchId: z.string(),
      finalTerms: zJobPositionSchema.pick({
        paymentType: true,
        paymentAmount: true,
        paymentRate: true,
        // ... other negotiated terms
      }),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    // 1. Validate match is in FINALIZING status
    // 2. Create JobPosition with negotiated terms
    // 3. Update match status to MATCHED
    // 4. Update job post status to FILLED
    // 5. Trigger shift generation
  });
```

#### 1.2 Add Match Step for Final Approval

**File**: `packages/api-medical/src/router/jobs/matches/steps.ts`

Enhance step workflow to include position creation step:

```typescript
// Add FINAL_APPROVAL step that triggers position creation
// This step should be the last step before MATCHED status
```

### Phase 2: Create Migration Utilities

#### 2.1 Data Migration Script

**File**: `packages/api-medical/src/migrations/applications-offers-to-matches.ts`

Create script to migrate existing applications/offers to matches:

```typescript
export async function migrateApplicationsToMatches() {
  // 1. Find all PENDING/ACCEPTED applications
  // 2. Create corresponding matches with appropriate status
  // 3. Create match steps based on application state
  // 4. Link existing positions to matches if they exist
}

export async function migrateOffersToMatches() {
  // Similar logic for offers
}
```

#### 2.2 Backward Compatibility Layer

**File**: `packages/api-medical/src/router/jobs/applications/legacy.ts`
**File**: `packages/api-medical/src/router/jobs/offers/legacy.ts`

Create compatibility layer that proxies old API calls to new matches API:

```typescript
// Proxy old application.create -> matches.create
// Proxy old application.approve -> matches.finalizeMatch
// etc.
```

### Phase 3: Update UI Components

#### 3.1 Provider Application Components

**Files to Update**:

- `apps/web-med/src/components/forms/ApplicationForm.tsx` → `MatchForm.tsx`
- `apps/web-med/src/www/providers/applications/` → `matches/`
- `apps/web-med/src/components/actions/jobs/application.tsx` → `match.tsx`

**Changes**:

- Replace application creation with match creation
- Update status displays to use match statuses
- Add step-based workflow UI

#### 3.2 Organization Offer Components

**Files to Update**:

- `apps/web-med/src/components/forms/OfferForm.tsx` → `MatchOfferForm.tsx`
- `apps/web-med/src/www/organizations/offers/` → `matches/`
- `apps/web-med/src/components/actions/jobs/offer.tsx` → `match.tsx`

**Changes**:

- Replace offer creation with match creation (organization-initiated)
- Update acceptance flow to use match workflow
- Add step management UI for organizations

#### 3.3 Status Components

**Files to Update**:

- `apps/web-med/src/components/common/ApplicationStatus.tsx` → `MatchStatus.tsx`
- `apps/web-med/src/components/common/OfferStatus.tsx` → `MatchStatus.tsx`

### Phase 4: Update Related Systems

#### 4.1 Notification System

**Files to Update**:

- `packages/api-medical/src/lib/actions/handleApplicationAction.ts` → Remove
- `packages/api-medical/src/lib/actions/handleOfferAction.ts` → Remove
- `packages/api-medical/src/lib/actions/handleMatchAction.ts` → Enhance

**Changes**:

- Migrate application/offer notifications to match notifications
- Add step-based notification triggers
- Update email templates for match workflow

#### 4.2 Action Logging

**Files to Update**:

- Remove `ActionType.CREATE_APPLICATION`, `ActionType.ACCEPT_APPLICATION`
- Remove `ActionType.CREATE_OFFER`, `ActionType.ACCEPT_OFFER`
- Enhance match-related action types

### Phase 5: Database Schema Updates

#### 5.1 Add Match References to Existing Models

```prisma
model JobPosition {
  // Add reference to match that created this position
  match     Match?  @relation(fields: [matchId], references: [id])
  matchId   String? @unique

  // Keep legacy references for migration period
  application   Application? @relation(fields: [applicationId], references: [id])
  applicationId String?      @unique
  offer         Offer?       @relation(fields: [offerId], references: [id])
  offerId       String?      @unique
}
```

#### 5.2 Migration Period Schema

During migration, maintain both old and new models with cross-references.

#### 5.3 Final Schema Cleanup

After migration is complete:

- Remove Application and Offer models
- Remove legacy references from JobPosition
- Clean up unused enums and types

## Implementation Steps

### Step 1: Enhance Matches API (Week 1)

1. Add position creation logic to matches router
2. Enhance match steps for final approval
3. Add comprehensive tests for new functionality

### Step 2: Create Migration Tools (Week 1)

1. Build data migration scripts
2. Create backward compatibility layer
3. Test migration with sample data

### Step 3: Update Provider UI (Week 2)

1. Replace application forms with match forms
2. Update provider dashboards and lists
3. Add match workflow UI components

### Step 4: Update Organization UI (Week 2)

1. Replace offer forms with match forms
2. Update organization dashboards
3. Add match management interfaces

### Step 5: System Integration (Week 3)

1. Update notification system
2. Migrate action logging
3. Update related APIs and services

### Step 6: Data Migration & Cleanup (Week 3)

1. Run data migration scripts
2. Verify data integrity
3. Remove legacy code and models

## Risk Mitigation

### Backward Compatibility

- Maintain legacy APIs during transition period
- Use feature flags to control rollout
- Provide data rollback capabilities

### Data Integrity

- Comprehensive migration testing
- Staged rollout with monitoring
- Backup and recovery procedures

### User Experience

- Gradual UI migration with user feedback
- Training materials for new workflow
- Support for both old and new processes during transition

## Success Criteria

1. **Functional**: All application/offer functionality replicated in matches
2. **Data**: 100% successful migration of existing data
3. **Performance**: No degradation in API response times
4. **UX**: Seamless transition for users with improved workflow
5. **Code Quality**: Removal of legacy code and improved maintainability

## Detailed File Changes

### API Files to Modify

#### Core Matches API Enhancement

- `packages/api-medical/src/router/jobs/matches/matches.ts`
  - Add `finalizeMatch` mutation for position creation
  - Add position creation logic with negotiated terms
  - Add job post status update to FILLED

#### Position Creation Logic

- `packages/api-medical/src/router/jobs/positions.ts`
  - Extract position creation logic into reusable function
  - Add match-based position creation pathway

#### Legacy API Deprecation

- `packages/api-medical/src/router/jobs/applications/applications-organizations.ts`

  - Mark `approve` and `reject` as deprecated
  - Add deprecation warnings and migration guidance

- `packages/api-medical/src/router/jobs/offers/offers-providers.ts`
  - Mark `accept` and `reject` as deprecated
  - Add deprecation warnings and migration guidance

### UI Files to Replace/Update

#### Provider-Side Components

```
apps/web-med/src/www/providers/applications/ → matches/
├── Applications.tsx → Matches.tsx
├── ApplicationCard.tsx → MatchCard.tsx
└── index.tsx → index.tsx

apps/web-med/src/www/providers/offers/ → (merge into matches/)
├── Offers.tsx → (integrate into Matches.tsx)
├── OfferCard.tsx → (integrate into MatchCard.tsx)
└── index.tsx → (remove)
```

#### Organization-Side Components

```
apps/web-med/src/www/organizations/job/prospecting/
├── ProspectingApplications.tsx → ProspectingMatches.tsx
├── ProspectingOffers.tsx → (merge into ProspectingMatches.tsx)
```

#### Form Components

```
apps/web-med/src/components/forms/
├── ApplicationForm.tsx → MatchApplicationForm.tsx
├── OfferForm.tsx → MatchOfferForm.tsx
```

#### Action Components

```
apps/web-med/src/components/actions/jobs/
├── application.tsx → match.tsx (provider actions)
├── offer.tsx → match.tsx (organization actions)
```

### Database Migration Scripts

#### Migration Files to Create

- `packages/db-medical/migrations/001_add_match_references.sql`
- `packages/db-medical/migrations/002_migrate_applications_to_matches.sql`
- `packages/db-medical/migrations/003_migrate_offers_to_matches.sql`
- `packages/db-medical/migrations/004_cleanup_legacy_models.sql`

### Notification System Updates

#### Action Handlers to Update

- `packages/api-medical/src/lib/actions/handleMatchAction.ts`
  - Add APPLICATION_CREATED → MATCH_CREATED mapping
  - Add OFFER_CREATED → MATCH_CREATED mapping
  - Add position creation notifications

#### Email Templates

- `packages/emails/emails/med/MatchCreated.tsx` (new)
- `packages/emails/emails/med/MatchAccepted.tsx` (new)
- `packages/emails/emails/med/MatchFinalized.tsx` (new)

### Router Integration

#### Main Router Updates

- `packages/api-medical/src/router/index.ts`
  - Ensure matches router is properly exposed
  - Add deprecation notices for applications/offers

#### Type Exports

- `packages/api-medical/src/types/index.ts`
  - Export new match-related types
  - Mark application/offer types as deprecated

## Testing Strategy

### Unit Tests

- Test match position creation logic
- Test migration scripts with sample data
- Test backward compatibility layer

### Integration Tests

- End-to-end match workflow testing
- UI component integration tests
- API endpoint migration tests

### User Acceptance Testing

- Provider match creation and acceptance flow
- Organization match initiation and management
- Match step workflow progression

## Timeline

- **Week 1**: API enhancements and migration tools
- **Week 2**: UI component updates
- **Week 3**: System integration and data migration
- **Week 4**: Testing, cleanup, and documentation

Total estimated effort: 4 weeks with 2-3 developers.
