import type { ExtendedPrismaClient } from "./factory";
import type { PrismaClientOptions } from ".prisma/client/medical/runtime/library.d.ts";

import { extendPrismaClient } from "./factory";
import { PrismaClient } from ".prisma/client/medical/edge";

export * from ".prisma/client/medical/edge";
export * from "./factory";

export default (() => {
  let prisma: PrismaClient | undefined;

  return function createClient(options?: PrismaClientOptions) {
    prisma ??= new PrismaClient(
      // @ts-expect-error - prisma types are not exposed for proper typing
      options,
    );

    return extendPrismaClient(
      prisma,
    ) as unknown as ExtendedPrismaClient<PrismaClient>;
  };
})();
