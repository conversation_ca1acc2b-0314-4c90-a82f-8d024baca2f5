import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { <PERSON>risma } from "@axa/database-medical";
import { PayType, StepStatus, StepType } from "@axa/database-medical";

import type { Context } from "../../../trpc";

import { ActionType, ResourceType } from "../../../constants/actions";
import { performAction } from "../../../lib/actions";
import { authorizedProcedure, createTRPCRouter } from "../../../trpc";
import { authorization } from "../../../utils/authorization";

/**
 * Rate negotiation state stored in MatchStep.metadata
 */
export interface RateNegotiationMetadata {
  // Current negotiation state
  currentOfferRate: number;
  lastOfferBy: "PROVIDER" | "ORGANIZATION";
  lastOfferAt: string; // ISO datetime
  offerExpiresAt?: string; // ISO datetime

  // Offer history (application-level tracking)
  offerHistory: RateOffer[];

  // Negotiation configuration
  strategy: "conservative" | "competitive" | "balanced" | "premium";
  allowCounterOffers: boolean;
  maxNegotiationRounds: number;
  currentRound: number;

  // Integration points
  threadId?: string; // For messaging
  compensationId: string; // JobCompensation record
}

/**
 * Individual rate offer within negotiation
 */
export interface RateOffer {
  id: string;
  proposedRate: number;
  madeBy: "PROVIDER" | "ORGANIZATION";
  madeAt: string; // ISO datetime
  message?: string;
  expiresAt?: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED" | "EXPIRED" | "WITHDRAWN";
  declineReason?: string;
  respondedAt?: string;
}

// Response type for rate offer operations
export type OfferResponse = "ACCEPT" | "DECLINE" | "COUNTER";

// Standard selections for all related data
const selection = {
  matchStep: {
    id: true,
    createdAt: true,
    updatedAt: true,
    startedAt: true,
    completedAt: true,
    type: true,
    status: true,
    order: true,
    isRequired: true,
    isSkippable: true,
    skipReason: true,
    isSuccessful: true,
    notes: true,
    metadata: true,
    matchId: true,
  },
  match: {
    id: true,
    status: true,
    organizationId: true,
    providerId: true,
    job: {
      select: {
        id: true,
        summary: true,
        allowRateNegotiation: true,
        minNegotiableRate: true,
        maxNegotiableRate: true,
      },
    },
    provider: {
      select: {
        id: true,
        person: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    },
    organization: {
      select: {
        id: true,
        name: true,
      },
    },
  },
  compensation: {
    id: true,
    minRate: true,
    maxRate: true,
    currentOfferRate: true,
    finalAgreedRate: true,
    rateStrategy: true,
    negotiationCount: true,
    negotiationStatus: true,
    lastOfferBy: true,
    offerExpiresAt: true,
    paymentType: true,
    matchId: true,
    jobId: true,
    providerId: true,
    organizationId: true,
  },
} satisfies {
  matchStep: Prisma.MatchStepSelect;
  match: Prisma.MatchSelect;
  compensation: Prisma.JobCompensationSelect;
};

/**
 * Helper function to validate rate is within job boundaries
 */
async function validateRateWithinBounds(
  rate: number,
  minRate: number,
  maxRate: number,
): Promise<void> {
  if (rate < minRate || rate > maxRate) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: `Rate must be between $${minRate} and $${maxRate}`,
    });
  }
}

/**
 * Helper function to determine who is making the offer
 */
function determineOfferMaker(
  userId: string,
  providerId: string,
  organizationId: string,
  organizationUserIds: string[],
): "PROVIDER" | "ORGANIZATION" {
  if (organizationUserIds.includes(userId)) {
    return "ORGANIZATION";
  }
  return "PROVIDER";
}

/**
 * Helper function to create a unique ID for offers
 */
function createId(): string {
  return `offer_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Helper function to get the rate negotiation step for a match
 */
async function getRateNegotiationStep(ctx: Context, matchId: string) {
  const step = await ctx.prisma.matchStep.findFirst({
    where: {
      matchId,
      type: StepType.RATE_NEGOTIATION,
    },
    select: {
      ...selection.matchStep,
      match: { select: selection.match },
    },
  });

  if (!step) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Rate negotiation step not found for this match",
    });
  }

  if (step.status !== StepStatus.IN_PROGRESS) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: `Rate negotiation step is not in progress (current status: ${step.status})`,
    });
  }

  if (!step.match?.job.allowRateNegotiation) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "This job does not allow rate negotiation",
    });
  }

  return step;
}

/**
 * Helper function to get job compensation record
 */
async function getJobCompensation(ctx: Context, compensationId: string) {
  const compensation = await ctx.prisma.jobCompensation.findUnique({
    where: { id: compensationId },
    select: selection.compensation,
  });

  if (!compensation) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Job compensation record not found",
    });
  }

  return compensation;
}

/**
 * Helper function to update step metadata using Prisma ORM
 */
async function updateStepMetadata(
  ctx: Context,
  stepId: string,
  metadata: RateNegotiationMetadata,
): Promise<void> {
  await ctx.prisma.matchStep.update({
    where: { id: stepId },
    data: {
      metadata: metadata as Prisma.JsonValue,
      updatedAt: new Date(),
    },
  });
}

/**
 * Helper function to update job compensation using Prisma ORM
 */
async function updateJobCompensation(
  ctx: Context,
  compensationId: string,
  data: {
    currentOfferRate?: number;
    finalAgreedRate?: number;
    lastOfferBy?: string;
    offerExpiresAt?: Date | null;
    negotiationCount?: number;
    negotiationStatus?: string;
  },
): Promise<void> {
  const updateData: Prisma.JobCompensationUpdateInput = {};

  if (data.currentOfferRate !== undefined) {
    updateData.currentOfferRate = data.currentOfferRate;
  }

  if (data.finalAgreedRate !== undefined) {
    updateData.finalAgreedRate = data.finalAgreedRate;
  }

  if (data.lastOfferBy !== undefined) {
    updateData.lastOfferBy = data.lastOfferBy;
  }

  if (data.offerExpiresAt !== undefined) {
    updateData.offerExpiresAt = data.offerExpiresAt;
  }

  if (data.negotiationCount !== undefined) {
    updateData.negotiationCount = data.negotiationCount;
  }

  if (data.negotiationStatus !== undefined) {
    updateData.negotiationStatus = data.negotiationStatus;
  }

  if (Object.keys(updateData).length === 0) {
    return;
  }

  await ctx.prisma.jobCompensation.update({
    where: { id: compensationId },
    data: updateData,
  });
}

/**
 * Helper function to get organization user IDs
 */
async function getOrganizationUserIds(
  ctx: Context,
  organizationId: string,
): Promise<string[]> {
  const orgUsers = await ctx.prisma.person.findMany({
    where: { organizationId },
    select: { id: true },
  });
  return orgUsers.map((u) => u.id);
}

export const rateNegotiationRouter = createTRPCRouter({
  /**
   * Initialize rate negotiation step
   */
  startNegotiation: authorizedProcedure
    .input(
      z.object({
        matchId: z.string(),
        initialRate: z.number().positive().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get match details
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.matchId },
        select: {
          id: true,
          status: true,
          initiator: true,
          organizationId: true,
          providerId: true,
          job: {
            select: {
              id: true,
              summary: true,
              allowRateNegotiation: true,
              minNegotiableRate: true,
              maxNegotiableRate: true,
            },
          },
          provider: {
            select: {
              id: true,
              person: { select: { firstName: true, lastName: true } },
            },
          },
          organization: { select: { id: true, name: true } },
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: match.providerId,
          organizationId: match.organizationId,
        },
      });

      // Validate job allows rate negotiation
      if (!match.job.allowRateNegotiation) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This job does not allow rate negotiation",
        });
      }

      // Validate initial rate if provided
      if (input.initialRate) {
        await validateRateWithinBounds(
          input.initialRate,
          match.job.minNegotiableRate || 0,
          match.job.maxNegotiableRate || 0,
        );
      }

      // Get organization users for determining offer maker
      const orgUserIds = await getOrganizationUserIds(
        ctx,
        match.organizationId,
      );

      // Determine who is initiating
      const initiatedBy = determineOfferMaker(
        ctx.user.id,
        match.providerId,
        match.organizationId,
        orgUserIds,
      );

      // Create or get rate negotiation step
      let step = await ctx.prisma.matchStep.findFirst({
        where: {
          matchId: input.matchId,
          type: StepType.RATE_NEGOTIATION,
        },
        select: selection.matchStep,
      });

      if (step) {
        if (step.status !== StepStatus.PENDING) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Rate negotiation already ${step.status.toLowerCase()}`,
          });
        }
        // Update existing step
        step = await ctx.prisma.matchStep.update({
          where: { id: step.id },
          data: {
            status: StepStatus.IN_PROGRESS,
            startedAt: new Date(),
            updatedAt: new Date(),
          },
          select: selection.matchStep,
        });
      } else {
        step = await ctx.prisma.matchStep.create({
          data: {
            type: StepType.RATE_NEGOTIATION,
            status: StepStatus.IN_PROGRESS,
            startedAt: new Date(),
            matchId: input.matchId,
            order: 1,
            isRequired: true,
            isSkippable: false,
          },
          select: selection.matchStep,
        });
      }

      // Create or get job compensation record
      let compensation = await ctx.prisma.jobCompensation.findFirst({
        where: { matchId: input.matchId },
        select: selection.compensation,
      });
      if (!compensation) {
        compensation = await ctx.prisma.jobCompensation.create({
          data: {
            minRate: match.job.minNegotiableRate || 0,
            maxRate: match.job.maxNegotiableRate || 0,
            currentOfferRate:
              input.initialRate || match.job.minNegotiableRate || 0,
            paymentType: PayType.HOURLY,
            rateStrategy: "balanced",
            negotiationStatus: "ACTIVE",
            matchId: input.matchId,
            jobId: match.job.id,
            providerId: match.providerId,
            organizationId: match.organizationId,
          },
          select: selection.compensation,
        });
      }

      // Initialize step metadata
      const metadata: RateNegotiationMetadata = {
        currentOfferRate: input.initialRate || compensation.minRate,
        lastOfferBy: initiatedBy,
        lastOfferAt: new Date().toISOString(),
        offerHistory: [],
        strategy:
          (compensation.rateStrategy as RateNegotiationMetadata["strategy"]) ||
          "balanced",
        allowCounterOffers: true,
        maxNegotiationRounds: 5,
        currentRound: 1,
        compensationId: compensation.id,
      };

      // If initial rate is provided, create first offer
      if (input.initialRate) {
        const initialOffer: RateOffer = {
          id: createId(),
          proposedRate: input.initialRate,
          madeBy: initiatedBy,
          madeAt: new Date().toISOString(),
          status: "PENDING",
        };
        metadata.offerHistory.push(initialOffer);
      }

      // Update step with metadata
      await ctx.prisma.matchStep.update({
        where: { id: step.id },
        data: { metadata: metadata as Prisma.JsonValue, updatedAt: new Date() },
      });

      // Update match status
      await ctx.prisma.match.update({
        where: { id: input.matchId },
        data: { status: "NEGOTIATING", updatedAt: new Date() },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.START_RATE_NEGOTIATION,
        resourceType: ResourceType.MATCH_STEP,
        resourceId: step.id,
        organizationId: match.organizationId,
        providerId: match.providerId,
        metadata: {
          matchId: match.id,
          jobSummary: match.job.summary,
          organizationName: match.organization.name,
          providerName: `${match.provider.person.firstName} ${match.provider.person.lastName}`,
          initialRate: input.initialRate || null,
          minRate: match.job.minNegotiableRate,
          maxRate: match.job.maxNegotiableRate,
        },
      });

      return {
        step: {
          id: step.id,
          status: StepStatus.IN_PROGRESS,
        },
        compensation: {
          id: compensation.id,
          minRate: compensation.minRate,
          maxRate: compensation.maxRate,
          currentOfferRate: input.initialRate || compensation.minRate,
        },
        metadata,
      };
    }),

  /**
   * Submit a new rate offer
   */
  submitOffer: authorizedProcedure
    .input(
      z.object({
        matchId: z.string(),
        proposedRate: z.number().positive(),
        message: z.string().optional(),
        expiresAt: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get rate negotiation step
      const step = await getRateNegotiationStep(ctx, input.matchId);

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: step.match.providerId,
          organizationId: step.match.organizationId,
        },
      });

      // Parse metadata
      const metadata = step.metadata as RateNegotiationMetadata;

      // Validate rate is within bounds
      await validateRateWithinBounds(
        input.proposedRate,
        step.match.job.minNegotiableRate || 0,
        step.match.job.maxNegotiableRate || 0,
      );

      // Get organization users for determining offer maker
      const orgUserIds = await getOrganizationUserIds(
        ctx,
        step.match.organizationId,
      );

      // Determine who is making the offer
      const madeBy = determineOfferMaker(
        ctx.user.id,
        step.match.providerId,
        step.match.organizationId,
        orgUserIds,
      );

      // Check if user is allowed to make an offer
      if (metadata.lastOfferBy === madeBy && metadata.offerHistory.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "You cannot make consecutive offers. Wait for the other party to respond.",
        });
      }

      // Check if max rounds reached
      if (metadata.currentRound > metadata.maxNegotiationRounds) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Maximum negotiation rounds (${metadata.maxNegotiationRounds}) reached`,
        });
      }

      // Create new offer
      const newOffer: RateOffer = {
        id: createId(),
        proposedRate: input.proposedRate,
        madeBy,
        madeAt: new Date().toISOString(),
        message: input.message,
        expiresAt: input.expiresAt?.toISOString(),
        status: "PENDING",
      };

      // Update metadata with new offer
      const updatedMetadata: RateNegotiationMetadata = {
        ...metadata,
        currentOfferRate: input.proposedRate,
        lastOfferBy: madeBy,
        lastOfferAt: newOffer.madeAt,
        offerExpiresAt: input.expiresAt?.toISOString(),
        offerHistory: [...metadata.offerHistory, newOffer],
        currentRound: metadata.currentRound + 1,
      };

      // Get compensation record
      const compensation = await getJobCompensation(
        ctx,
        metadata.compensationId,
      );

      // Update step metadata and compensation
      await Promise.all([
        updateStepMetadata(ctx, step.id, updatedMetadata),
        updateJobCompensation(ctx, compensation.id, {
          currentOfferRate: input.proposedRate,
          lastOfferBy: madeBy,
          offerExpiresAt: input.expiresAt,
          negotiationCount: compensation.negotiationCount + 1,
          negotiationStatus: "ACTIVE",
        }),
      ]);

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.SUBMIT_RATE_OFFER,
        resourceType: ResourceType.MATCH_STEP,
        resourceId: step.id,
        organizationId: step.match.organizationId,
        providerId: step.match.providerId,
        metadata: {
          matchId: step.matchId,
          proposedRate: input.proposedRate,
          madeBy,
          message: input.message || null,
          jobSummary: step.match.job.summary,
        },
      });

      return {
        offer: newOffer,
        metadata: updatedMetadata,
      };
    }),

  /**
   * Respond to an existing rate offer
   */
  respondToOffer: authorizedProcedure
    .input(
      z.object({
        matchId: z.string(),
        offerId: z.string(),
        response: z.enum(["ACCEPT", "DECLINE", "COUNTER"]),
        counterOfferRate: z.number().positive().optional(),
        declineReason: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get rate negotiation step
      const step = await getRateNegotiationStep(ctx, input.matchId);

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: step.match.providerId,
          organizationId: step.match.organizationId,
        },
      });

      // Parse metadata
      const metadata = step.metadata as RateNegotiationMetadata;

      // Find the offer
      const offerIndex = metadata.offerHistory.findIndex(
        (o) => o.id === input.offerId,
      );

      if (offerIndex === -1) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Offer not found",
        });
      }

      const offer = metadata.offerHistory[offerIndex];

      if (!offer || offer.status !== "PENDING") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Offer is already ${offer?.status?.toLowerCase() || "invalid"}`,
        });
      }

      // Get organization users for determining offer maker
      const orgUserIds = await getOrganizationUserIds(
        ctx,
        step.match.organizationId,
      );

      // Determine who is responding
      const respondedBy = determineOfferMaker(
        ctx.user.id,
        step.match.providerId,
        step.match.organizationId,
        orgUserIds,
      );

      // Check if user is allowed to respond to this offer
      if (offer.madeBy === respondedBy) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You cannot respond to your own offer",
        });
      }

      // Update offer status
      offer.status = input.response === "ACCEPT" ? "ACCEPTED" : "DECLINED";
      offer.respondedAt = new Date().toISOString();

      if (input.declineReason) {
        offer.declineReason = input.declineReason;
      }

      // Update metadata
      metadata.offerHistory[offerIndex] = offer;

      // Handle response type
      const result: Record<string, unknown> = { offer };
      let actionType: ActionType;

      if (input.response === "ACCEPT") {
        // Finalize negotiation
        actionType = ActionType.ACCEPT_RATE_OFFER;

        // Update compensation with final rate
        await updateJobCompensation(ctx, metadata.compensationId, {
          finalAgreedRate: offer.proposedRate,
          negotiationStatus: "FINALIZED",
        });

        // Update step status
        await ctx.prisma.matchStep.update({
          where: { id: step.id },
          data: {
            status: StepStatus.COMPLETED,
            completedAt: new Date(),
            isSuccessful: true,
            notes: `Rate finalized at $${offer.proposedRate}`,
            updatedAt: new Date(),
          },
        });

        // Update match status
        await ctx.prisma.match.update({
          where: { id: input.matchId },
          data: { status: "FINALIZING", updatedAt: new Date() },
        });

        result.finalized = true;
      } else if (input.response === "COUNTER" && input.counterOfferRate) {
        // Submit counter offer
        actionType = ActionType.COUNTER_RATE_OFFER;

        // Validate counter rate
        await validateRateWithinBounds(
          input.counterOfferRate,
          step.match.job.minNegotiableRate || 0,
          step.match.job.maxNegotiableRate || 0,
        );

        // Create counter offer
        const counterOffer: RateOffer = {
          id: createId(),
          proposedRate: input.counterOfferRate,
          madeBy: respondedBy,
          madeAt: new Date().toISOString(),
          message: input.message,
          status: "PENDING",
        };

        // Update metadata
        metadata.offerHistory.push(counterOffer);
        metadata.currentOfferRate = input.counterOfferRate;
        metadata.lastOfferBy = respondedBy;
        metadata.lastOfferAt = counterOffer.madeAt;
        metadata.currentRound = metadata.currentRound + 1;

        // Update compensation
        await updateJobCompensation(ctx, metadata.compensationId, {
          currentOfferRate: input.counterOfferRate,
          lastOfferBy: respondedBy,
          negotiationCount: metadata.offerHistory.length,
        });

        result.counterOffer = counterOffer;
      } else {
        // Decline without counter
        actionType = ActionType.DECLINE_RATE_OFFER;
      }

      // Update metadata
      await updateStepMetadata(ctx, step.id, metadata);

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: actionType,
        resourceType: ResourceType.MATCH_STEP,
        resourceId: step.id,
        organizationId: step.match.organizationId,
        providerId: step.match.providerId,
        metadata: {
          matchId: step.matchId,
          offerId: input.offerId,
          proposedRate: offer.proposedRate,
          response: input.response,
          counterOfferRate: input.counterOfferRate || null,
          declineReason: input.declineReason || null,
          jobSummary: step.match.job.summary,
        },
      });

      return {
        ...result,
        metadata,
      };
    }),

  /**
   * Finalize rate negotiation
   */
  finalizeNegotiation: authorizedProcedure
    .input(
      z.object({
        matchId: z.string(),
        agreedRate: z.number().positive(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Get rate negotiation step
      const step = await getRateNegotiationStep(ctx, input.matchId);

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: step.match.providerId,
          organizationId: step.match.organizationId,
        },
      });

      // Parse metadata
      const metadata = step.metadata as RateNegotiationMetadata;

      // Validate rate is within bounds
      await validateRateWithinBounds(
        input.agreedRate,
        step.match.job.minNegotiableRate || 0,
        step.match.job.maxNegotiableRate || 0,
      );

      // Get compensation record
      const compensation = await getJobCompensation(
        ctx,
        metadata.compensationId,
      );

      // Update compensation with final rate
      await updateJobCompensation(ctx, compensation.id, {
        finalAgreedRate: input.agreedRate,
        negotiationStatus: "FINALIZED",
      });

      // Update job post with negotiated rate (if jobPost model exists)
      try {
        await ctx.prisma.jobPost.update({
          where: { id: step.match.job.id },
          data: {
            finalNegotiatedRate: input.agreedRate,
            updatedAt: new Date(),
          },
        });
      } catch (error) {
        // If jobPost doesn't have finalNegotiatedRate field, continue without error
        console.warn("Could not update job post finalNegotiatedRate:", error);
      }

      // Complete the rate negotiation step
      await ctx.prisma.matchStep.update({
        where: { id: step.id },
        data: {
          status: StepStatus.COMPLETED,
          completedAt: new Date(),
          isSuccessful: true,
          notes: input.notes || `Rate finalized at $${input.agreedRate}`,
          updatedAt: new Date(),
        },
      });

      // Update match status
      await ctx.prisma.match.update({
        where: { id: input.matchId },
        data: { status: "FINALIZING", updatedAt: new Date() },
      });

      // Log action
      await performAction({
        actorId: ctx.user.id,
        type: ActionType.FINALIZE_RATE_NEGOTIATION,
        resourceType: ResourceType.MATCH_STEP,
        resourceId: step.id,
        organizationId: step.match.organizationId,
        providerId: step.match.providerId,
        metadata: {
          matchId: step.matchId,
          finalRate: input.agreedRate,
          jobSummary: step.match.job.summary,
        },
      });

      return {
        finalRate: input.agreedRate,
        completed: true,
      };
    }),

  /**
   * Get rate negotiation history
   */
  getHistory: authorizedProcedure
    .input(z.object({ matchId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Get match details using Prisma ORM
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.matchId },
        select: {
          id: true,
          organizationId: true,
          providerId: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      // Apply authorization
      await authorization({
        ctx,
        input: {
          providerId: match.providerId,
          organizationId: match.organizationId,
        },
      });

      // Get rate negotiation step using Prisma ORM
      const step = await ctx.prisma.matchStep.findFirst({
        where: {
          matchId: input.matchId,
          type: StepType.RATE_NEGOTIATION,
        },
        select: {
          id: true,
          type: true,
          status: true,
          metadata: true,
          createdAt: true,
          startedAt: true,
          completedAt: true,
        },
      });

      if (!step) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Rate negotiation step not found",
        });
      }

      // Parse metadata
      const metadata = step.metadata as RateNegotiationMetadata;

      // Get compensation record using Prisma ORM
      const compensation = await ctx.prisma.jobCompensation.findFirst({
        where: { matchId: input.matchId },
        select: {
          id: true,
          minRate: true,
          maxRate: true,
          currentOfferRate: true,
          finalAgreedRate: true,
          rateStrategy: true,
          negotiationCount: true,
          negotiationStatus: true,
        },
      });

      return {
        step: {
          id: step.id,
          status: step.status,
          createdAt: step.createdAt,
          startedAt: step.startedAt,
          completedAt: step.completedAt,
        },
        compensation: compensation
          ? {
              id: compensation.id,
              minRate: compensation.minRate,
              maxRate: compensation.maxRate,
              currentOfferRate: compensation.currentOfferRate,
              finalAgreedRate: compensation.finalAgreedRate,
              rateStrategy: compensation.rateStrategy,
              negotiationCount: compensation.negotiationCount,
              negotiationStatus: compensation.negotiationStatus,
            }
          : null,
        metadata,
      };
    }),
});
