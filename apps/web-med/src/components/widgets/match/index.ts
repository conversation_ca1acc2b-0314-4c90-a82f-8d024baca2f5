// Main widget components
export { MatchWidget } from "./MatchWidget";
export { MatchRateNegotiation } from "./MatchRateNegotiation";

// Individual components
export {
  MatchProviderCard,
  MatchJobCard,
  MatchRateInsights,
  MatchNegotiationPanel,
  MatchCockpit,
} from "./components";

// Context
export { MatchProvider, useMatch } from "./context/MatchContext";

// Types
export type { MatchWidgetProps } from "./MatchWidget";
export type { MatchRateNegotiationProps } from "./MatchRateNegotiation";
export type { MatchContextValue } from "./context/MatchContext";
export type {
  MatchProviderCardProps,
  MatchJobCardProps,
  MatchRateInsightsProps,
  MatchNegotiationPanelProps,
  MatchCockpitProps,
} from "./components";
