import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Prisma } from "@axa/database-medical";
import { ValueType } from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils";

import type { ProcedureResult } from "../types/select";

import { authorizedProcedure, createTRPCRouter } from "../trpc";

// Base enums
const zValueTypeSchema = z.nativeEnum(ValueType);

// Core schema (direct fields)
const zCoreValueSchema = z.object({
  type: zValueTypeSchema,
  value: z.string(),
});

// Extended schema with relations
const zValueSchema = zCoreValueSchema.extend({
  organizationId: z.string().optional(),
});

// Schema for controlling nested selections
const zIncludeSchema = z.object({
  organization: z.boolean().default(false),
  deleted: z.boolean().default(false),
});

const selection = {
  value: {
    id: true,
    type: true,
    value: true,
    createdAt: true,
    updatedAt: true,
    organizationId: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  settings: {
    id: true,
    defaultMode: true,
    offerExpirationDays: true,
    contractExpirationDays: true,
    defaultShiftDuration: true,
  },
} satisfies {
  value: Prisma.ValueSelect;
  organization: Prisma.OrganizationSelect;
  settings: Prisma.OrganizationSettingSelect;
};

export const valuesRouter = createTRPCRouter({
  get: authorizedProcedure
    .input(z.object({ id: z.string(), include: zIncludeSchema.optional() }))
    .query(async ({ ctx, input }) => {
      const include = input.include ?? { organization: false, deleted: false };
      const select = {
        ...selection.value,
        organization: include.organization
          ? {
              select: {
                ...selection.organization,
                settings: {
                  select: selection.settings,
                },
              },
            }
          : undefined,
      } satisfies Prisma.ValueSelect;

      const value = (await ctx.prisma.value.findUnique({
        where: {
          id: input.id,
          deletedAt: include.deleted ? undefined : null,
        },
        select,
      })) as unknown as ProcedureResult<
        typeof select,
        Prisma.$ValuePayload
      > | null;

      if (!value) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Value not found",
        });
      }

      // Check access
      if (!ctx.isInternal && value.organizationId !== ctx.organization?.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have access to this value",
        });
      }

      return value;
    }),

  getMany: authorizedProcedure
    .input(
      z.object({
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        type: zValueTypeSchema.optional(),
        include: zIncludeSchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const include = input.include ?? { organization: false, deleted: false };
      const select = {
        ...selection.value,
        organization: include.organization
          ? { select: selection.organization }
          : undefined,
      } satisfies Prisma.ValueSelect;

      type Result = ProcedureResult<typeof select>;

      const where = {
        type: input.type,
        deletedAt: include.deleted ? undefined : null,
        OR: input.query
          ? [{ value: { contains: input.query, mode: "insensitive" } }]
          : undefined,
      } satisfies Prisma.ValueWhereInput;

      const [total, rawItems] = await Promise.all([
        ctx.prisma.value.count({ where }),
        ctx.prisma.value.findMany({
          where,
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          orderBy: { value: "asc" },
          select,
        }),
      ]);

      const items = rawItems as unknown as Result[];

      return {
        items,
        total,
      };
    }),

  create: authorizedProcedure
    .input(zValueSchema)
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.prisma.value.findFirst({
        where: {
          type: input.type,
          value: input.value,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Value already exists for this type in your organization",
        });
      }

      return ctx.prisma.value.create({
        data: {
          type: input.type,
          value: input.value,
          organization: ctx.organization?.id
            ? { connect: { id: ctx.organization.id } }
            : undefined,
        },
        select: { id: true },
      });
    }),

  update: authorizedProcedure
    .input(
      z.object({
        id: z.string(),
        data: zCoreValueSchema.partial(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const value = await ctx.prisma.value.findUnique({
        where: { id: input.id },
        select: { id: true, organizationId: true, type: true },
      });

      if (!value) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Value not found",
        });
      }

      // Check access
      if (!ctx.isInternal && value.organizationId !== ctx.organization?.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have access to this value",
        });
      }

      // If updating value, check for duplicates
      if (input.data.value) {
        const existing = await ctx.prisma.value.findFirst({
          where: {
            type: input.data.type ?? value.type,
            value: input.data.value,
            id: { not: input.id },
          },
        });

        if (existing) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Value already exists for this type",
          });
        }
      }

      return ctx.prisma.value.update({
        where: { id: input.id },
        data: input.data,
        select: { id: true },
      });
    }),

  delete: authorizedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const value = await ctx.prisma.value.findUnique({
        where: { id: input.id },
        select: { id: true, organizationId: true },
      });

      if (!value) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Value not found",
        });
      }

      // Check access
      if (!ctx.isInternal && value.organizationId !== ctx.organization?.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have access to this value",
        });
      }

      if (ctx.options.permanentDelete) {
        return ctx.prisma.value.delete({
          where: { id: input.id },
          select: { id: true },
        });
      }

      return ctx.prisma.value.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
        select: { id: true },
      });
    }),
});
