"use client";

import { useMemo } from "react";

import type { Milestone, Registry } from "@axa/ui/blocks/negotiation-timeline";
import { NegotiationTimeline } from "@axa/ui/blocks/negotiation-timeline";
import {
  MessageBlock,
  RateOfferBlock,
  RateNegotiatedBlock,
} from "@axa/ui/blocks/negotiation-timeline";
import { MessageSquare } from "lucide-react";

import { useMatch } from "../context/MatchContext";

// Registry for negotiation blocks
const negotiationRegistry: Registry = {
  message: MessageBlock,
  "rate-offer": RateOfferBlock,
  "rate-negotiated": RateNegotiatedBlock,
};

export interface MatchNegotiationPanelProps {
  className?: string;
  defaultExpanded?: boolean;
  showMessageInput?: boolean;
  showHeader?: boolean;
}

export function MatchNegotiationPanel({ 
  className,
  defaultExpanded = true,
  showMessageInput = true,
  showHeader = true,
}: MatchNegotiationPanelProps) {
  const { 
    loading,
    match,
    currentRate,
    canAcceptRate,
    canCounterRate,
    acceptRate,
    counterRate,
    sendMessage,
    providerName,
  } = useMatch();

  // Transform match data into timeline milestones
  const milestones = useMemo((): Milestone[] => {
    if (!match) return [];

    const timelineMilestones: Milestone[] = [];

    // Rate Negotiation Milestone
    if (match.compensation || match.status === "NEGOTIATING") {
      const rateBlocks = [];

      // Current rate offer block
      if (currentRate) {
        const isProvider = match.compensation?.lastOfferBy === match.providerId;
        const isPending = match.compensation?.negotiationStatus === "ACTIVE";

        rateBlocks.push({
          id: `rate-offer-${match.compensation?.id || 'current'}`,
          type: "rate-offer" as const,
          timestamp: new Date(match.compensation?.updatedAt || match.createdAt),
          data: {
            rate: `$${currentRate}/hr`,
            status: isPending ? ("pending" as const) : ("accepted" as const),
            expectedParty: isPending ? (isProvider ? "organization" : "provider") : undefined,
            onAccept: canAcceptRate ? () => acceptRate(currentRate) : undefined,
            onCounter: canCounterRate ? () => {
              const counterRateValue = prompt(`Counter offer (current: $${currentRate}/hr):`);
              if (counterRateValue && !isNaN(parseFloat(counterRateValue))) {
                counterRate(parseFloat(counterRateValue));
              }
            } : undefined,
          },
        });
      }

      // Final negotiated rate (if completed)
      if (match.compensation?.finalAgreedRate) {
        rateBlocks.push({
          id: `rate-final-${match.compensation.id}`,
          type: "rate-negotiated" as const,
          timestamp: new Date(match.compensation.updatedAt || match.createdAt),
          data: {
            doctor: providerName,
            rate: `$${match.compensation.finalAgreedRate}/hr`,
            message: "Rate negotiation completed successfully.",
            onViewAgreement: () => {
              alert("View agreement functionality coming soon");
            },
            onViewNegotiationHistory: () => {
              alert("View negotiation history coming soon");
            },
          },
        });
      }

      if (rateBlocks.length > 0) {
        timelineMilestones.push({
          id: "rate-negotiation",
          title: "Rate Negotiation",
          status: match.status === "MATCHED" ? "completed" : "active",
          timestamp: new Date(match.createdAt),
          blocks: rateBlocks,
        });
      }
    }

    // Initial match creation milestone
    timelineMilestones.push({
      id: "match-created",
      title: "Match Created",
      status: "completed",
      timestamp: new Date(match.createdAt),
      blocks: [
        {
          id: `match-init-${match.id}`,
          type: "message" as const,
          timestamp: new Date(match.createdAt),
          data: {
            author: match.initiator === "PROVIDER" ? "Provider" : "Organization",
            message: match.initiationNote || "Match initiated for this position.",
            timestamp: new Date(match.createdAt),
          },
        },
      ],
    });

    // Sort milestones by timestamp (newest first)
    return timelineMilestones.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, [
    match, 
    currentRate, 
    canAcceptRate, 
    canCounterRate, 
    acceptRate, 
    counterRate, 
    providerName
  ]);

  const header = showHeader ? (
    <div className="flex items-center gap-2 border-b bg-background/50 px-4 py-3">
      <MessageSquare className="size-5 text-teal-600" />
      <h2 className="text-lg font-semibold text-foreground">Negotiations</h2>
    </div>
  ) : undefined;

  return (
    <div className={className}>
      <NegotiationTimeline
        className="h-full border border-border"
        milestones={milestones}
        registry={negotiationRegistry}
        defaultExpanded={defaultExpanded}
        showMessageInput={showMessageInput}
        onSendMessage={sendMessage}
        loading={loading}
        header={header}
      />
    </div>
  );
}
